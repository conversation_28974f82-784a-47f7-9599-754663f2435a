/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/route";
exports.ids = ["app/api/keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/route.ts */ \"(rsc)/./src/app/api/keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/route\",\n        pathname: \"/api/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/keys/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n\n// cookies function is not directly needed here if createSupabaseServerClientOnRequest handles it\n\n // encrypt returns a single string now\n // Import Node.js crypto module\n// POST /api/keys\n// Adds a new API key to a specific custom_api_config\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in POST /api/keys:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    try {\n        const keyData = await request.json();\n        const { custom_api_config_id, provider, predefined_model_id, api_key_raw, label, temperature = 1.0 } = keyData;\n        if (!custom_api_config_id || !provider || !predefined_model_id || !api_key_raw || !label) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label'\n            }, {\n                status: 400\n            });\n        }\n        // Validate temperature\n        if (temperature < 0.0 || temperature > 2.0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temperature must be between 0.0 and 2.0'\n            }, {\n                status: 400\n            });\n        }\n        if (typeof api_key_raw !== 'string' || api_key_raw.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key cannot be empty.'\n            }, {\n                status: 400\n            });\n        }\n        // ROKEY_ENCRYPTION_KEY is checked within encryption.ts now, but good to be aware of its necessity.\n        // const encryptionKey = process.env.ROKEY_ENCRYPTION_KEY;\n        // if (!encryptionKey) {\n        //   console.error('ROKEY_ENCRYPTION_KEY is not set.');\n        //   return NextResponse.json({ error: 'Server configuration error: Encryption key not found.' }, { status: 500 });\n        // }\n        // encrypt function now returns a single string: iv:authTag:encryptedData\n        const encrypted_api_key_combined = (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_2__.encrypt)(api_key_raw);\n        const api_key_hash = crypto__WEBPACK_IMPORTED_MODULE_3___default().createHash('sha256').update(api_key_raw).digest('hex');\n        // The type Omit<ApiKey, ...> will align because ApiKey no longer has 'iv'\n        const newDbKey = {\n            custom_api_config_id,\n            provider,\n            predefined_model_id,\n            encrypted_api_key: encrypted_api_key_combined,\n            label,\n            api_key_hash,\n            status: 'active',\n            is_default_general_chat_model: false,\n            temperature,\n            user_id: user.id\n        };\n        const { data, error } = await supabase.from('api_keys').insert(newDbKey).select().single();\n        if (error) {\n            console.error('Supabase error creating API key:', error);\n            if (error.code === '23503') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid custom_api_config_id or predefined_model_id.',\n                    details: error.message\n                }, {\n                    status: 400\n                });\n            }\n            if (error.code === '23505') {\n                // Check if this is the new unique_model_per_config constraint\n                if (error.message.includes('unique_model_per_config')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.',\n                        details: error.message\n                    }, {\n                        status: 409\n                    });\n                }\n                // Fallback for other unique constraint violations\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'A unique constraint was violated.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to save API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // If this is the first key for the config, or no other key is default, make this one default.\n        if (data) {\n            const { data: existingDefaults, error: defaultCheckError } = await supabase.from('api_keys').select('id').eq('custom_api_config_id', custom_api_config_id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n            .eq('is_default_general_chat_model', true).neq('id', data.id) // Exclude the newly added key itself from this check\n            .limit(1);\n            if (defaultCheckError) {\n                console.error('Error checking for existing default keys:', defaultCheckError);\n            // Proceed without making it default, but log the error. The key is still saved.\n            } else if (!existingDefaults || existingDefaults.length === 0) {\n                // No other key is default, so make this new one default\n                const { data: updatedKey, error: updateError } = await supabase.from('api_keys').update({\n                    is_default_general_chat_model: true\n                }).eq('id', data.id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n                .select().single();\n                if (updateError) {\n                    console.error('Error updating new key to be default:', updateError);\n                // Key is saved, but failed to make it default. Log and proceed.\n                } else {\n                    // Successfully made the new key default, return this updated key data\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedKey, {\n                        status: 201\n                    });\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        // Catch errors from encrypt function (e.g., if ROKEY_ENCRYPTION_KEY was invalid)\n        if (e.message.includes('Invalid ROKEY_ENCRYPTION_KEY') || e.message.includes('Encryption input must be a non-empty string')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server-side encryption error',\n                details: e.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/keys?custom_config_id=<ID>\n// Retrieves all API keys for a specific custom_api_config_id\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in GET /api/keys:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const customConfigId = searchParams.get('custom_config_id');\n    if (!customConfigId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'custom_config_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { data: keys, error } = await supabase// Ensure selected fields match DisplayApiKey definition\n        .from('api_keys').select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').eq('custom_api_config_id', customConfigId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Supabase error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const displayKeys = (keys || []).map((key)=>({\n                id: key.id,\n                custom_api_config_id: key.custom_api_config_id,\n                provider: key.provider,\n                predefined_model_id: key.predefined_model_id,\n                label: key.label,\n                status: key.status,\n                temperature: key.temperature,\n                created_at: key.created_at,\n                last_used_at: key.last_used_at,\n                is_default_general_chat_model: key.is_default_general_chat_model\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(displayKeys, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/keys:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/keys?id=<ID>\n// Updates an existing API key (currently supports temperature updates)\nasync function PUT(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in PUT /api/keys:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to update API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const keyId = searchParams.get('id');\n    if (!keyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const updateData = await request.json();\n        const { temperature, predefined_model_id } = updateData;\n        // Prepare update object\n        const updateFields = {};\n        if (temperature !== undefined) {\n            // Validate temperature\n            if (temperature < 0.0 || temperature > 2.0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Temperature must be between 0.0 and 2.0'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.temperature = temperature;\n        }\n        if (predefined_model_id !== undefined) {\n            if (typeof predefined_model_id !== 'string' || predefined_model_id.trim().length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Model ID must be a non-empty string'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.predefined_model_id = predefined_model_id;\n        }\n        // If no fields to update, return error\n        if (Object.keys(updateFields).length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No valid fields provided for update'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('api_keys').update(updateFields).eq('id', keyId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').single();\n        if (error) {\n            console.error('Supabase error updating API key:', error);\n            if (error.code === '23505' && error.message.includes('unique_model_per_config')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'This model is already configured in this setup. Each model can only be used once per configuration.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in PUT /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ALGORITHM = 'aes-256-gcm';\nconst IV_LENGTH = 12; // Recommended for GCM\nconst AUTH_TAG_LENGTH = 16; // GCM produces a 16-byte auth tag\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\nconst key = Buffer.from(ROKEY_ENCRYPTION_KEY_FROM_ENV, 'hex');\nfunction encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(IV_LENGTH);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipheriv(ALGORITHM, key, iv);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    const authTag = cipher.getAuthTag();\n    // Prepend IV and authTag to the encrypted text (hex encoded)\n    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;\n}\nfunction decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = Buffer.from(parts[0], 'hex');\n    const authTag = Buffer.from(parts[1], 'hex');\n    const encryptedData = parts[2];\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== AUTH_TAG_LENGTH) {\n        throw new Error(`Invalid authTag length. Expected ${AUTH_TAG_LENGTH} bytes.`);\n    }\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipheriv(ALGORITHM, key, iv);\n    decipher.setAuthTag(authTag);\n    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();