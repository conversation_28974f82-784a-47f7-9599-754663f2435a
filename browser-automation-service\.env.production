# Browser Automation Service - PRODUCTION Configuration

# Service Configuration
SERVICE_HOST=0.0.0.0
SERVICE_PORT=8000
SERVICE_ENV=production
DEBUG=false

# RouKey Integration - PRODUCTION DOMAIN
ROKEY_API_URL=https://roukey.online
ROKEY_API_SECRET=rokey-browser-automation-secret-2025

# Database Configuration (Supabase) - Production values
SUPABASE_URL=https://hpkzzhpufhbxtxqaugjh.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODcwNDYyNiwiZXhwIjoyMDY0MjgwNjI2fQ.WeK0fdR1xihnA64WI9IRTzJK7PkRPPPhH8NahXJ0ESk

# Google Search API - Production values
GOOGLE_SEARCH_API_KEY=AIzaSyA9tb1SaqqPgJCuMfrKc01MxoiwvFtZXww
GOOGLE_CSE_ID=f6c7431e2a5a54a7c

# Classification API - Production Gemini key
ROKEY_CLASSIFICATION_GEMINI_API_KEY=AIzaSyBqJf8vgz2DcEOy-XN8lOlXGcsmoLDb10s

# Memory Configuration (Qdrant Vector Store)
# Production should use a proper Qdrant server, not in-memory
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=browser_automation_memory

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080
BROWSER_SLOW_MO=0
BROWSER_DEVTOOLS=false

# Session Management
SESSION_POOL_SIZE=20
SESSION_TIMEOUT=300
MAX_CONCURRENT_SESSIONS=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security
JWT_SECRET_KEY=rokey-browser-automation-jwt-secret-2025
CORS_ORIGINS=https://roukey.online,https://www.roukey.online

# Performance - Production optimized
MAX_WORKERS=8
REQUEST_TIMEOUT=300
RATE_LIMIT_PER_MINUTE=120

# User Tier Configuration
USER_TIERS=free,starter,professional,enterprise
TIER_BROWSER_AUTOMATION_LIMITS=0,15,-1,-1
TIER_BROWSER_AUTOMATION_ACCESS=false,true,true,true
