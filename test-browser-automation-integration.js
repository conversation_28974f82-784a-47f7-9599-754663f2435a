/**
 * Test Browser Automation Integration
 * Tests the complete flow from RouKey app to browser automation service
 */

const fetch = require('node-fetch');

const CONFIG_ID = 'b39270c0-feb8-4c99-b6d2-9ee224edd57e';
const TEST_TASK = 'Find the price of iPhone 15 on Amazon';

async function testCompleteFlow() {
  console.log('🧪 Testing Complete Browser Automation Flow');
  console.log('=' .repeat(50));

  try {
    // Step 1: Test browser automation classification
    console.log('\n1️⃣ Testing Browser Automation Classification...');
    const classificationResponse = await fetch('http://localhost:3000/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key' // Your auth bypass should handle this
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          { role: 'user', content: TEST_TASK }
        ],
        custom_api_config_id: CONFIG_ID,
        stream: false
      })
    });

    if (classificationResponse.ok) {
      const classificationResult = await classificationResponse.json();
      console.log('✅ Classification successful');
      console.log('   Response type:', typeof classificationResult);
      
      // Check if it detected browser automation
      if (classificationResult.choices && classificationResult.choices[0]) {
        const content = classificationResult.choices[0].message.content;
        console.log('   Content preview:', content.substring(0, 100) + '...');
      }
    } else {
      console.log('❌ Classification failed:', classificationResponse.status);
      const error = await classificationResponse.text();
      console.log('   Error:', error);
      return;
    }

    // Step 2: Test role classification endpoint
    console.log('\n2️⃣ Testing Role Classification Endpoint...');
    const roleClassificationResponse = await fetch('http://localhost:3000/api/orchestration/classify-roles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      body: JSON.stringify({
        task: TEST_TASK,
        available_roles: ['shopping_assistant', 'web_research', 'general_chat'],
        user_config: { config_id: CONFIG_ID }
      })
    });

    if (roleClassificationResponse.ok) {
      const roleResult = await roleClassificationResponse.json();
      console.log('✅ Role classification successful');
      console.log('   Classified roles:', roleResult.roles);
      console.log('   Reasoning:', roleResult.reasoning);
    } else {
      console.log('❌ Role classification failed:', roleClassificationResponse.status);
      const error = await roleClassificationResponse.text();
      console.log('   Error:', error);
      return;
    }

    // Step 3: Test browser automation service health
    console.log('\n3️⃣ Testing Browser Automation Service Health...');
    try {
      const healthResponse = await fetch('http://localhost:8000/health/', {
        method: 'GET',
        timeout: 5000
      });

      if (healthResponse.ok) {
        const healthResult = await healthResponse.json();
        console.log('✅ Browser automation service is healthy');
        console.log('   Status:', healthResult.status);
      } else {
        console.log('❌ Browser automation service health check failed:', healthResponse.status);
      }
    } catch (error) {
      console.log('❌ Browser automation service is not running');
      console.log('   Error:', error.message);
      console.log('   💡 Start the service with: cd browser-automation-service && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000');
      return;
    }

    // Step 4: Test browser automation execution endpoint
    console.log('\n4️⃣ Testing Browser Automation Execution...');
    const browserExecutionResponse = await fetch('http://localhost:3000/api/browser-automation/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      body: JSON.stringify({
        task: TEST_TASK,
        config_id: CONFIG_ID,
        task_type: 'data_extraction',
        user_tier: 'professional',
        stream: false
      })
    });

    if (browserExecutionResponse.ok) {
      const executionResult = await browserExecutionResponse.json();
      console.log('✅ Browser automation execution successful');
      console.log('   Task ID:', executionResult.task_id);
      console.log('   Success:', executionResult.success);
      console.log('   Result preview:', JSON.stringify(executionResult.result).substring(0, 200) + '...');
    } else {
      console.log('❌ Browser automation execution failed:', browserExecutionResponse.status);
      const error = await browserExecutionResponse.text();
      console.log('   Error:', error);
    }

    console.log('\n🎯 Integration Test Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

async function testBrowserAutomationToggle() {
  console.log('\n🔧 Testing Browser Automation Toggle...');
  
  try {
    // Test getting current status
    const statusResponse = await fetch(`http://localhost:3000/api/custom-configs/${CONFIG_ID}/browser-automation-status`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-key'
      }
    });

    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('✅ Browser automation status retrieved');
      console.log('   Enabled:', status.enabled);
      console.log('   User tier:', status.user_tier);
    } else {
      console.log('❌ Failed to get browser automation status:', statusResponse.status);
    }

    // Test toggling the setting
    const toggleResponse = await fetch(`http://localhost:3000/api/custom-configs/${CONFIG_ID}/browser-automation`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      body: JSON.stringify({
        enabled: true
      })
    });

    if (toggleResponse.ok) {
      const toggleResult = await toggleResponse.json();
      console.log('✅ Browser automation toggle successful');
      console.log('   New status:', toggleResult);
    } else {
      console.log('❌ Failed to toggle browser automation:', toggleResponse.status);
    }

  } catch (error) {
    console.error('❌ Toggle test failed:', error);
  }
}

// Run the tests
async function runAllTests() {
  await testBrowserAutomationToggle();
  await testCompleteFlow();
}

runAllTests().catch(console.error);
