#!/usr/bin/env python3
"""
Smart startup script for RouKey Browser Automation Service
Automatically detects environment and loads appropriate configuration
"""

import os
import sys
import subprocess
from pathlib import Path

def detect_environment():
    """Detect if we're in development or production"""
    # Check for explicit environment variable
    env = os.getenv('SERVICE_ENV', '').lower()
    if env in ['production', 'prod']:
        return 'production'
    elif env in ['development', 'dev']:
        return 'development'
    
    # Auto-detect based on various indicators
    indicators = {
        'production': [
            os.getenv('NODE_ENV') == 'production',
            os.getenv('VERCEL') is not None,
            os.getenv('RAILWAY_ENVIRONMENT') is not None,
            os.getenv('RENDER') is not None,
            'prod' in os.getcwd().lower(),
            not os.path.exists('.git'),  # Production deployments often don't have .git
        ],
        'development': [
            os.getenv('NODE_ENV') == 'development',
            os.path.exists('.git'),
            'localhost' in os.getenv('ROKEY_API_URL', ''),
            os.path.exists('requirements.txt'),  # Dev environment likely has requirements
        ]
    }
    
    prod_score = sum(indicators['production'])
    dev_score = sum(indicators['development'])
    
    if prod_score > dev_score:
        return 'production'
    else:
        return 'development'

def load_environment_file(environment):
    """Load the appropriate environment file"""
    env_files = {
        'development': ['.env', '.env.local', '.env.development'],
        'production': ['.env.production', '.env.prod', '.env']
    }
    
    for env_file in env_files[environment]:
        if os.path.exists(env_file):
            print(f"📁 Loading environment from: {env_file}")
            # Set environment variable to load this file
            os.environ['ENV_FILE'] = env_file
            return env_file
    
    print(f"⚠️ No environment file found for {environment}")
    return None

def validate_configuration(environment):
    """Validate that required configuration is present"""
    required_vars = {
        'development': [
            'ROKEY_API_URL',
            'ROKEY_CLASSIFICATION_GEMINI_API_KEY'
        ],
        'production': [
            'ROKEY_API_URL',
            'ROKEY_CLASSIFICATION_GEMINI_API_KEY',
            'SUPABASE_URL',
            'SUPABASE_SERVICE_ROLE_KEY'
        ]
    }
    
    missing_vars = []
    for var in required_vars[environment]:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables for {environment}:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    return True

def print_startup_info(environment, env_file):
    """Print startup information"""
    print("🚀 RouKey Browser Automation Service")
    print("=" * 50)
    print(f"🌍 Environment: {environment}")
    print(f"📁 Config file: {env_file or 'None'}")
    print(f"🔗 RouKey API: {os.getenv('ROKEY_API_URL', 'Not set')}")
    print(f"🏠 Service Host: {os.getenv('SERVICE_HOST', 'localhost')}")
    print(f"🔌 Service Port: {os.getenv('SERVICE_PORT', '8000')}")
    print(f"🐛 Debug Mode: {os.getenv('DEBUG', 'false')}")
    print("=" * 50)

def start_service():
    """Start the FastAPI service with appropriate configuration"""
    # Detect environment
    environment = detect_environment()
    print(f"🔍 Detected environment: {environment}")
    
    # Load environment file
    env_file = load_environment_file(environment)
    
    # Validate configuration
    if not validate_configuration(environment):
        print("❌ Configuration validation failed. Please check your environment variables.")
        sys.exit(1)
    
    # Print startup info
    print_startup_info(environment, env_file)
    
    # Prepare uvicorn command
    host = os.getenv('SERVICE_HOST', '0.0.0.0')
    port = int(os.getenv('SERVICE_PORT', '8000'))
    reload = environment == 'development'
    workers = 1 if reload else int(os.getenv('MAX_WORKERS', '4'))
    log_level = os.getenv('LOG_LEVEL', 'info').lower()
    
    cmd = [
        'python', '-m', 'uvicorn',
        'app.main:app',
        '--host', host,
        '--port', str(port),
        '--log-level', log_level
    ]
    
    if reload:
        cmd.append('--reload')
    else:
        cmd.extend(['--workers', str(workers)])
    
    print(f"🚀 Starting service with command: {' '.join(cmd)}")
    print("📚 API Documentation will be available at:")
    print(f"   http://{host}:{port}/docs")
    print("\n🔄 Starting server...")
    
    # Start the service
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Service stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Service failed to start: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_service()
