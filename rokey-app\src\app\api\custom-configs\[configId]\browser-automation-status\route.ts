import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    configId: string;
  }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    const { configId } = await params;

    // Get authenticated user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Verify the config belongs to the user
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, user_id, browser_automation_enabled')
      .eq('id', configId)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
    }

    // Get user tier from subscriptions table
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    return NextResponse.json({
      enabled: config.browser_automation_enabled || false,
      user_tier: userTier,
      config_id: configId
    });

  } catch (error) {
    console.error('Error fetching browser automation status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
