"""
RouKey Integration Service
Handles communication with RouKey's existing systems for role classification and user configs
"""

import httpx
from typing import Dict, List, Any, Optional
from app.core.config import settings
from app.core.logging import LoggerMixin
from app.core.exceptions import RouKeyIntegrationException


class RouKeyIntegration(LoggerMixin):
    """
    Integration service for communicating with RouKey's existing systems
    
    This service:
    1. Gets user configurations and API keys from RouKey
    2. Uses RouKey's Gemini classifier for role determination
    3. Respects RouKey's routing strategies and tier limits
    4. Reports usage and analytics back to Rou<PERSON>ey
    """
    
    def __init__(self):
        self.base_url = settings.ROKEY_API_URL
        self.api_secret = settings.ROKEY_API_SECRET
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_secret}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )
    
    async def get_user_config(self, user_id: str, config_id: str) -> Dict[str, Any]:
        """
        Get user's custom configuration from <PERSON>ou<PERSON><PERSON>
        
        Returns:
        - User's API keys per role
        - Routing strategy preferences
        - Role configurations
        - Tier information
        """
        try:
            self.log_info(
                "Fetching user configuration",
                user_id=user_id,
                config_id=config_id
            )
            
            # Production implementation: Call RouKey's API directly
            try:
                response = await self.client.get(
                    f"/api/custom-configs/{config_id}",
                    params={"user_id": user_id}
                )
                response.raise_for_status()

                config = response.json()
                self.log_info(
                    "User configuration retrieved from RouKey API",
                    user_id=user_id,
                    config_id=config_id,
                    roles_count=len(config.get("roles", {}))
                )

                return config

            except httpx.HTTPError as api_error:
                self.log_error(f"RouKey API error: {api_error}")
                # Fallback to mock only if API is unavailable and in debug mode
                if settings.DEBUG:
                    self.log_info("Falling back to mock configuration")
                    return await self._get_mock_user_config(user_id, config_id)
                raise
            
        except httpx.HTTPError as e:
            self.log_error(f"Failed to get user config: {e}")
            raise RouKeyIntegrationException(f"Failed to retrieve user configuration: {e}")
        except Exception as e:
            self.log_error(f"Unexpected error getting user config: {e}")
            raise RouKeyIntegrationException(f"Configuration retrieval error: {e}")
    
    async def classify_task_roles(self, task: str, user_config: Dict[str, Any], api_keys: List[Dict[str, Any]] = None) -> List[str]:
        """
        Use RouKey's BYOK system to determine required roles for the task
        Integrates with existing intelligent role routing using user's API keys

        Args:
            task: The user's task description
            user_config: User's configuration with available roles
            api_keys: User's BYOK API keys with role assignments

        Returns:
            List of role names that should handle this task
        """
        try:
            self.log_info("Classifying task roles using BYOK system", task=task[:100])

            # Extract roles from user's API key configurations (BYOK system)
            if api_keys:
                available_roles = []
                for api_key in api_keys:
                    roles = api_key.get("roles", [])
                    available_roles.extend(roles)

                # Remove duplicates and prioritize browser-relevant roles
                unique_roles = list(set(available_roles))

                if unique_roles:
                    # Prioritize browser/web-related roles for browser automation
                    browser_roles = [role for role in unique_roles if any(keyword in role.lower()
                        for keyword in ["browser", "web", "automation", "research", "shopping", "navigation"])]

                    if browser_roles:
                        self.log_info("Using browser-specific roles from BYOK", roles=browser_roles[:3])
                        return browser_roles[:3]  # Limit to 3 roles for performance
                    else:
                        self.log_info("Using available user roles from BYOK", roles=unique_roles[:3])
                        return unique_roles[:3]

            # Fallback: Use RouKey's classification API if no BYOK roles available
            try:
                response = await self.client.post(
                    "/api/orchestration/classify-roles",
                    json={
                        "task": task,
                        "available_roles": list(user_config.get("roles", {}).keys()),
                        "user_config": user_config
                    }
                )
                response.raise_for_status()

                classification = response.json()
                classified_roles = classification.get("roles", [])

                self.log_info(
                    "Task classification completed via RouKey API",
                    task=task[:100],
                    classified_roles=classified_roles
                )

                return classified_roles

            except httpx.HTTPError as api_error:
                self.log_error(f"RouKey classification API error: {api_error}")
                # NO MOCK FALLBACKS - This is a commercial app!
                raise Exception(f"Role classification failed: {api_error}. RouKey API must be available for browser automation.")
            
        except httpx.HTTPError as e:
            self.log_error(f"Failed to classify task roles: {e}")
            raise Exception(f"Role classification failed: {e}. RouKey API must be available for browser automation.")
        except Exception as e:
            self.log_error(f"Unexpected error in role classification: {e}")
            raise Exception(f"Role classification failed: {e}. System error in browser automation.")
    
    async def get_llm_for_role(
        self, 
        role: str, 
        user_config: Dict[str, Any], 
        routing_strategy: str
    ) -> Dict[str, Any]:
        """
        Get the appropriate LLM configuration for a role based on routing strategy
        
        Args:
            role: The role name
            user_config: User's configuration
            routing_strategy: RouKey's routing strategy (complex_routing, strict_fallback, etc.)
            
        Returns:
            LLM configuration with API key, model, and parameters
        """
        try:
            roles_config = user_config.get("roles", {})
            role_config = roles_config.get(role)
            
            if not role_config:
                # Fallback to general_chat or first available role
                fallback_role = "general_chat"
                if fallback_role not in roles_config and roles_config:
                    fallback_role = list(roles_config.keys())[0]
                role_config = roles_config.get(fallback_role, {})
            
            # Apply routing strategy
            llm_config = await self._apply_routing_strategy(
                role_config, 
                routing_strategy, 
                user_config
            )
            
            self.log_info(
                "LLM configuration retrieved",
                role=role,
                routing_strategy=routing_strategy,
                provider=llm_config.get("provider"),
                model=llm_config.get("model")
            )
            
            return llm_config
            
        except Exception as e:
            self.log_error(f"Failed to get LLM for role {role}: {e}")
            raise RouKeyIntegrationException(f"LLM configuration error: {e}")
    
    async def report_usage(
        self, 
        user_id: str, 
        task_id: str, 
        usage_data: Dict[str, Any]
    ):
        """Report usage statistics back to RouKey for billing and analytics"""
        try:
            # Production implementation: Always report to RouKey API
            try:
                await self.client.post(
                    "/api/analytics/browser-automation-usage",
                    json={
                        "user_id": user_id,
                        "task_id": task_id,
                        "timestamp": usage_data.get("timestamp"),
                        "tokens_used": usage_data.get("tokens_used", 0),
                        "steps_completed": usage_data.get("steps_completed", 0),
                        "execution_time_ms": usage_data.get("execution_time_ms", 0),
                        "success": usage_data.get("success", False),
                        "roles_used": usage_data.get("roles_used", []),
                        "routing_strategy": usage_data.get("routing_strategy"),
                        "user_tier": usage_data.get("user_tier")
                    }
                )

                self.log_info("Usage reported to RouKey API", user_id=user_id, task_id=task_id)

            except httpx.HTTPError as api_error:
                self.log_error(f"Failed to report usage to RouKey API: {api_error}")
                # In debug mode, log mock usage reporting
                if settings.DEBUG:
                    self.log_info("Mock usage reporting (API unavailable)", user_id=user_id, usage_data=usage_data)
            
        except Exception as e:
            self.log_error(f"Failed to report usage: {e}")
            # Don't raise exception - usage reporting shouldn't break the flow
    
    async def _apply_routing_strategy(
        self, 
        role_config: Dict[str, Any], 
        strategy: str, 
        user_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply RouKey's routing strategy to select appropriate LLM"""
        
        api_keys = role_config.get("api_keys", [])
        if not api_keys:
            raise RouKeyIntegrationException("No API keys configured for role")
        
        if strategy == "complex_routing":
            # Use complexity-based routing (would need task complexity analysis)
            # For now, use first available key
            selected_key = api_keys[0]
        elif strategy == "strict_fallback":
            # Use primary key, fallback on failure
            selected_key = api_keys[0]  # Primary
        elif strategy == "ab_testing":
            # A/B test between available keys
            import random
            selected_key = random.choice(api_keys)
        else:
            # Default routing
            selected_key = api_keys[0]
        
        return {
            "api_key": selected_key.get("key"),
            "provider": selected_key.get("provider"),
            "model": selected_key.get("model"),
            "temperature": role_config.get("temperature", 0.7),
            "max_tokens": role_config.get("max_tokens", 2000)
        }
    
    async def _get_mock_user_config(self, user_id: str, config_id: str) -> Dict[str, Any]:
        """Mock user configuration for development/testing"""
        return {
            "user_id": user_id,
            "config_id": config_id,
            "browsing_enabled": True,
            "roles": {
                "general_chat": {
                    "api_keys": [{
                        "key": settings.OPENAI_API_KEY or "mock-openai-key",
                        "provider": "openai",
                        "model": "gpt-4o"
                    }],
                    "temperature": 0.7,
                    "max_tokens": 2000
                },
                "web_research": {
                    "api_keys": [{
                        "key": settings.OPENAI_API_KEY or "mock-openai-key",
                        "provider": "openai",
                        "model": "gpt-4o"
                    }],
                    "temperature": 0.5,
                    "max_tokens": 3000
                },
                "shopping_assistant": {
                    "api_keys": [{
                        "key": settings.ANTHROPIC_API_KEY or "mock-anthropic-key",
                        "provider": "anthropic",
                        "model": "claude-3-sonnet-********"
                    }],
                    "temperature": 0.6,
                    "max_tokens": 2500
                }
            },
            "routing_strategy": "complex_routing",
            "tier": "pro",
            "monthly_limit": -1,  # Unlimited for pro
            "features": {
                "browsing_automation": True,
                "google_search_verification": True,
                "memory_enabled": True
            }
        }
    
    # REMOVED: No mock classification in commercial app - all classification must use RouKey's Gemini API
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
