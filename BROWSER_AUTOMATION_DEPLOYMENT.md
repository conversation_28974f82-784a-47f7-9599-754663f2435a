# Browser Automation Service Deployment Guide

## 🚨 CRITICAL: Environment Configuration

The browser automation service requires proper environment configuration to work in both development and production.

## 🔧 Development Setup

### Main RouKey App (.env.local)
```bash
# Browser Automation Service URL
BROWSER_AUTOMATION_SERVICE_URL=http://localhost:8000
```

### Browser Automation Service (.env)
```bash
# Service Configuration
SERVICE_HOST=localhost
SERVICE_PORT=8000
SERVICE_ENV=development
DEBUG=true

# RouKey Integration
ROKEY_API_URL=http://localhost:3000
ROKEY_API_SECRET=rokey-browser-automation-secret-2025
```

## 🚀 Production Setup

### Main RouKey App (Production Environment)
```bash
# Browser Automation Service URL - MUST BE SET TO YOUR ACTUAL DOMAIN
BROWSER_AUTOMATION_SERVICE_URL=https://browser-automation.roukey.online
```

### Browser Automation Service (.env.production)
```bash
# Service Configuration
SERVICE_HOST=0.0.0.0
SERVICE_PORT=8000
SERVICE_ENV=production
DEBUG=false

# RouKey Integration - MUST BE SET TO YOUR ACTUAL DOMAIN
ROKEY_API_URL=https://roukey.online
ROKEY_API_SECRET=rokey-browser-automation-secret-2025
```

## 🌐 Deployment Options

### Option 1: Same Server (Recommended for simplicity)
- Deploy browser automation service on the same server as your main app
- Use different ports (main app: 3000, browser service: 8000)
- Set `BROWSER_AUTOMATION_SERVICE_URL=http://localhost:8000` in production

### Option 2: Separate Server/Subdomain
- Deploy browser automation service on a separate server or subdomain
- Example: `https://browser-automation.roukey.online`
- Set `BROWSER_AUTOMATION_SERVICE_URL=https://browser-automation.roukey.online`

### Option 3: Docker Compose (Recommended for production)
```yaml
version: '3.8'
services:
  rokey-app:
    build: ./rokey-app
    ports:
      - "3000:3000"
    environment:
      - BROWSER_AUTOMATION_SERVICE_URL=http://browser-automation:8000
    depends_on:
      - browser-automation

  browser-automation:
    build: ./browser-automation-service
    ports:
      - "8000:8000"
    environment:
      - ROKEY_API_URL=http://rokey-app:3000
      - SERVICE_ENV=production
```

## ⚠️ Common Issues

### Issue 1: Service Can't Connect
**Problem**: Browser automation service can't reach main RouKey app
**Solution**: Ensure `ROKEY_API_URL` is set correctly in browser service

### Issue 2: Main App Can't Reach Browser Service
**Problem**: Main app gets connection refused when calling browser service
**Solution**: Ensure `BROWSER_AUTOMATION_SERVICE_URL` is set correctly in main app

### Issue 3: CORS Issues
**Problem**: Browser requests blocked by CORS
**Solution**: Update `CORS_ORIGINS` in browser service to include your domain

## 🔍 Testing the Connection

### 1. Test Browser Service Health
```bash
curl http://localhost:8000/health/
# Should return: {"status": "healthy"}
```

### 2. Test Main App to Browser Service
```bash
curl -X POST http://localhost:3000/api/browser-automation/execute \
  -H "Content-Type: application/json" \
  -d '{"task": "test", "config_id": "test"}'
```

### 3. Test Browser Service to Main App
```bash
curl -X POST http://localhost:8000/api/v1/browser/execute \
  -H "Content-Type: application/json" \
  -d '{"task": "test", "user_id": "test", "config_id": "test"}'
```

## 📋 Pre-Deployment Checklist

- [ ] `BROWSER_AUTOMATION_SERVICE_URL` set in main app environment
- [ ] `ROKEY_API_URL` set in browser service environment
- [ ] Both services can reach each other
- [ ] CORS origins configured correctly
- [ ] API secrets match between services
- [ ] Health endpoints return 200 OK
- [ ] Classification endpoint `/api/orchestration/classify-roles` exists in main app

## 🚨 Security Notes

1. **API Secrets**: Use strong, unique secrets for `ROKEY_API_SECRET`
2. **CORS**: Only allow your actual domains in `CORS_ORIGINS`
3. **Firewall**: Restrict browser service port access if on separate server
4. **HTTPS**: Always use HTTPS in production for both services

## 🔄 Environment Variables Summary

| Variable | Main App | Browser Service | Description |
|----------|----------|-----------------|-------------|
| `BROWSER_AUTOMATION_SERVICE_URL` | ✅ | ❌ | URL where browser service is running |
| `ROKEY_API_URL` | ❌ | ✅ | URL where main RouKey app is running |
| `ROKEY_API_SECRET` | ❌ | ✅ | Secret for browser service to authenticate with main app |
| `CORS_ORIGINS` | ❌ | ✅ | Allowed origins for browser service |

This ensures your browser automation works seamlessly in both development and production! 🎯
